import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/providers/AuthProvider';
import { useNavigate, NavLink } from 'react-router-dom';
import { useMutation } from '@/hooks/use-api';
import { authAPI } from '@/services/api';
import { RegisterData } from '@/types/api';
import { getErrorMessage } from '@/utils/api-helpers';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { cn } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const formSchema = z
  .object({
    fullName: z.string().min(3, { message: 'Full name is required' }),
    dob: z.date({ required_error: 'Date of birth is required' }),
    email: z.string().email({ message: 'Invalid email address' }),
    mobile: z
      .string()
      .min(10, { message: 'Mobile number must be at least 10 digits' })
      .max(10),
    aadhaar: z
      .string()
      .min(12, { message: 'Aadhaar number must be 12 digits' })
      .max(12),
    password: z
      .string()
      .min(8, { message: 'Password must be at least 8 characters' })
      .max(50)
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,}$/,
        {
          message:
            'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
        }
      ),
    confirmPassword: z.string(),
    category: z.string({ required_error: 'Please select a category' }),
    passwordStrength: z.number().min(0).max(100).optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const RegistrationForm = () => {
  const [passwordStrength, setPasswordStrength] = useState(0);
  const { toast } = useToast();
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      email: '',
      mobile: '',
      aadhaar: '',
      password: '',
      confirmPassword: '',
      category: '',
      dob: undefined,
      passwordStrength: 0,
    },
  });

  // Calculate password strength
  const calculatePasswordStrength = (password: string): number => {
    if (!password) return 0;

    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 20;
    if (password.length >= 12) strength += 10;

    // Character variety checks
    if (/[a-z]/.test(password)) strength += 10;
    if (/[A-Z]/.test(password)) strength += 15;
    if (/[0-9]/.test(password)) strength += 15;
    if (/[^A-Za-z0-9]/.test(password)) strength += 15;

    // Complexity checks
    if (/[a-z].*[a-z].*[a-z]/.test(password)) strength += 5;
    if (/[A-Z].*[A-Z]/.test(password)) strength += 5;
    if (/[0-9].*[0-9]/.test(password)) strength += 5;
    if (/[^A-Za-z0-9].*[^A-Za-z0-9]/.test(password)) strength += 5;

    return Math.min(strength, 100);
  };

  // Handle password change to update strength
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'password') {
        const strength = calculatePasswordStrength(value.password as string);
        setPasswordStrength(strength);
        form.setValue('passwordStrength', strength);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Use the useMutation hook for registration
  const { mutate: registerMutation, isLoading: isRegistering } = useMutation(
    (data: RegisterData) => authAPI.register(data),
    {
      onSuccess: () => {
        toast({
          title: 'Registration Successful',
          description:
            'Your account has been created successfully. Please check your email and verify your mobile number.',
        });

        // Reset form
        form.reset();

        // Navigate to mobile verification page with the mobile number
        navigate('/verify-mobile', {
          state: {
            mobile: form.getValues().mobile,
            message:
              'Please enter the OTP sent to your mobile number to complete verification.',
          },
        });
      },
      onError: (error) => {
        const errorMessage = getErrorMessage(
          error,
          'Registration failed. Please try again.'
        );

        toast({
          title: 'Registration Failed',
          description: errorMessage,
          variant: 'destructive',
        });
      },
    }
  );

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Format date of birth to ISO string
    const dobIso = values.dob.toISOString();

    // Prepare registration data
    const userData: RegisterData = {
      fullName: values.fullName,
      email: values.email,
      mobile: values.mobile,
      password: values.password,
      aadhaar: values.aadhaar,
      category: values.category as 'state_police' | 'capf' | 'civilian',
      dob: dobIso,
    };

    // Call the register mutation
    registerMutation(userData);
  };

  return (
    <div className="w-full">
      <Card className="shadow-lg border border-gray-200">
        <CardHeader className="bg-white border-b border-gray-200">
          <CardTitle className="text-xl font-bold text-gyaan-navy">
            Registration Form
          </CardTitle>
          <CardDescription className="text-gray-600">
            Fill in your details to create an account
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 bg-white">
          <div className="flex items-center gap-2 p-3 bg-blue-50 text-blue-800 rounded-md mb-6 border border-blue-200">
            <Info className="h-4 w-4" />
            <p className="text-sm">All fields marked with * are mandatory</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Personal Information Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4">
                  Personal Information
                </h3>
                <div className="w-16 h-1 bg-gyaan-gold mb-4"></div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Full Name *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your full name"
                            {...field}
                            className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dob"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel className="text-gyaan-navy font-medium">
                          Date of Birth *
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full pl-3 text-left font-normal border border-gray-300 hover:border-gyaan-navy',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                {field.value ? (
                                  format(field.value, 'PPP')
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() ||
                                date < new Date('1940-01-01')
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Contact Information Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4">
                  Contact Information
                </h3>
                <div className="w-16 h-1 bg-gyaan-gold mb-4"></div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Email Address *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="mobile"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Mobile Number * (for OTP verification)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="10-digit mobile number"
                            {...field}
                            className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Identification Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4">
                  Identification
                </h3>
                <div className="w-16 h-1 bg-gyaan-gold mb-4"></div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="aadhaar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Aadhaar Number *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="12-digit Aadhaar number"
                            {...field}
                            maxLength={12}
                            className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                            onChange={(e) => {
                              // Only allow digits
                              const value = e.target.value.replace(/\D/g, '');
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormDescription className="text-gray-600 text-xs">
                          Your Aadhaar number is stored securely and masked in
                          our system.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Category *
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy">
                              <SelectValue placeholder="Select your category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="state-police">
                              State Police
                            </SelectItem>
                            <SelectItem value="capf">CAPF</SelectItem>
                            <SelectItem value="civilian">
                              Civilian/Other
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Security Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4">
                  Account Security
                </h3>
                <div className="w-16 h-1 bg-gyaan-gold mb-4"></div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Create Password *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Create a strong password"
                            {...field}
                            className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                          />
                        </FormControl>
                        <div className="mt-2">
                          <div className="text-xs text-gray-600">
                            Password should contain uppercase, lowercase,
                            numbers, and special characters
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gyaan-navy font-medium">
                          Confirm Password *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Confirm your password"
                            {...field}
                            className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Submit Button Section */}
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <p className="text-sm text-gray-600">
                    By registering, you agree to our{' '}
                    <a href="#" className="text-gyaan-maroon hover:underline">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="#" className="text-gyaan-maroon hover:underline">
                      Privacy Policy
                    </a>
                    .
                  </p>
                </div>
                <Button
                  type="submit"
                  className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white py-3"
                  disabled={isRegistering}
                >
                  {isRegistering ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Registering...
                    </>
                  ) : (
                    'Register Account'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="bg-gray-50 p-4 border-t border-gray-200 flex justify-between items-center">
          <p className="text-sm text-gray-600">Already have an account?</p>
          <NavLink to="/login">
            <Button
              variant="outline"
              className="border-gyaan-navy text-gyaan-navy hover:bg-gyaan-navy/10"
            >
              Login
            </Button>
          </NavLink>
        </CardFooter>
      </Card>
    </div>
  );
};

export default RegistrationForm;
